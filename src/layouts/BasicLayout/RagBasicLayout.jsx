import { useEffect } from 'react';
import { Outlet, useLocation } from 'umi';
import moment from 'moment';
import 'moment/locale/zh-cn';
import { Layout, message, notification } from 'antd';
import classnames from 'classnames';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import styles from './BasicLayout.module.less';

moment.locale('zh-cn');

function BasicLayout(props) {
    const { getRagUserInfo, className } = props;
    const location = useLocation();
    const [, notificationContextHolder] = notification.useNotification();
    const [, contextHolder] = message.useMessage();

    const isRagRoute = location.pathname.includes('/qe_rag');
    const func = async () => {
        if (isRagRoute) {
            try {
                await getRagUserInfo();
            } catch (err) {
                console.error('RAG用户认证失败:', err);
            }
        }
    };

    // web 加载时直接获取
    useEffect(() => {
        func();
    }, []);

    // 监听路由变化，处理RAG认证
    useEffect(() => {
        const handleRagAuth = async () => {
            if (isRagRoute) {
                try {
                    await getRagUserInfo();
                } catch (err) {
                    console.error('路由变化 - RAG用户认证失败:', err);
                }
            }
        };
        handleRagAuth();
    }, [location.pathname]);

    return (
        <div>
            {contextHolder}
            {notificationContextHolder}
            <Layout className={classnames(className, styles.layoutContainer)}>
                <Layout className={styles.right}>
                    <Outlet key={1} />
                </Layout>
            </Layout>
        </div>
    );
}

export default connectModel([baseModel], (state) => ({
    ragUserInfo: state.common.base.ragUserInfo
}))(BasicLayout);
