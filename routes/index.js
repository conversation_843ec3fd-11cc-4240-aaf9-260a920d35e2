// common
import itp from './itp';
import icafe from './icafe';
// front_qe_tools
import { frontQeToolsRoutes } from './front_qe_tools/index';
// rdEffectToolsRoutes
import { rdEffectToolsRoutes } from './rd_effect_tools/index';
// qe_rag
import { qeRagRoutes } from './qe_rag/index';

// rag  路由配置
const ragRoutes = [
    {
        path: '/',
        redirect: '/qe_rag'
    },
    {
        path: '/',
        component: '@/layouts/BasicLayout/RagBasicLayout',
        routes: [
            // QE RAG 平台
            ...qeRagRoutes,
            { path: '/*', component: '@/pages/404/404.jsx' }
        ]
    }
];

// qamate 路由配置
const qamateRoutes = [
    {
        path: '/',
        redirect: '/case/index'
    },
    {
        path: '/',
        component: '@/layouts/BasicLayout',
        routes: [
            itp,
            icafe,
            // 大前端质效工具模块
            ...frontQeToolsRoutes,
            // 研发提效
            ...rdEffectToolsRoutes,
            { path: '/*', component: '@/pages/404/404.jsx' }
        ]
    }
];

const routes = {
    qamate: qamateRoutes,
    rag: ragRoutes
};

export default routes[process.env.platform || 'qamate'];
